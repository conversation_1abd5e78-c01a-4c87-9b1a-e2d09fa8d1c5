feat: 增强用户通知系统的日志记录功能

- 新增统一的通知日志记录模块 (common/logger/notification.go)
  - 支持结构化JSON格式日志记录
  - 包含时间戳、用户ID、通知类型、事件类型、状态等详细信息
  - 实现敏感数据自动清理功能，防止记录token、密码等敏感信息
  - 支持成功、警告、错误等不同级别的日志记录

- 增强各种通知方式的日志记录功能
  - 邮件通知: 新增SendEmailWithLogging函数，记录收件人、主题、发送状态、错误信息等
  - Webhook通知: 新增sendBalanceWarningWebhookWithLogging函数，记录请求URL、响应状态码、响应内容等
  - 企业微信机器人: 新增sendBalanceWarningQyWxBotWithLogging函数，记录消息内容、响应状态等
  - 钉钉机器人: 新增sendBalanceWarningDingtalkWithLogging函数，记录发送详情和响应信息
  - WxPusher: 新增sendBalanceWarningWxPusherWithLogging函数，记录API调用详情和响应状态

- 更新余额预警通知系统
  - 将所有余额预警通知发送函数更新为使用带日志记录的版本
  - 保持向后兼容性，同时记录旧格式日志

- 更新测试通知功能
  - 测试邮件发送功能使用新的日志记录版本
  - 提供更详细的调试信息

- 性能优化
  - 添加HTTP请求超时控制
  - 记录每次通知的执行时间，便于性能分析
  - 限制响应体大小记录，避免日志过大

此次更新大大提高了通知系统的可观测性和故障排查能力，
便于定位通知发送失败的具体原因和性能瓶颈。