#!/bin/bash

# 设置日志清理定时任务的脚本

# 配置参数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CLEANUP_SCRIPT="$SCRIPT_DIR/cleanup_logs.sh"
LOG_DIR="${LOG_DIR:-$PROJECT_DIR/logs}"
KEEP_DAYS="${KEEP_DAYS:-7}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
日志清理定时任务设置脚本

用法: $0 [选项] [操作]

操作:
    install     安装定时任务 (默认)
    uninstall   卸载定时任务
    status      查看定时任务状态
    test        测试清理脚本

选项:
    -d, --dir DIR       指定日志目录 (默认: $PROJECT_DIR/logs)
    -k, --keep DAYS     指定保留天数 (默认: 7)
    -t, --time TIME     指定执行时间 (默认: "0 2 * * *" 每天凌晨2点)
    -h, --help          显示此帮助信息

示例:
    $0                                  # 安装默认定时任务
    $0 install -k 14                   # 安装定时任务，保留14天日志
    $0 install -t "0 3 * * *"          # 安装定时任务，每天凌晨3点执行
    $0 uninstall                       # 卸载定时任务
    $0 status                          # 查看定时任务状态
    $0 test                            # 测试清理脚本

定时任务格式说明:
    "0 2 * * *"     每天凌晨2点执行
    "0 */6 * * *"   每6小时执行一次
    "0 2 * * 0"     每周日凌晨2点执行

EOF
}

# 默认参数
CRON_TIME="0 2 * * *"  # 每天凌晨2点执行
ACTION="install"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        install|uninstall|status|test)
            ACTION="$1"
            shift
            ;;
        -d|--dir)
            LOG_DIR="$2"
            shift 2
            ;;
        -k|--keep)
            KEEP_DAYS="$2"
            shift 2
            ;;
        -t|--time)
            CRON_TIME="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查清理脚本是否存在
if [[ ! -f "$CLEANUP_SCRIPT" ]]; then
    log_error "清理脚本不存在: $CLEANUP_SCRIPT"
    exit 1
fi

# 确保清理脚本可执行
chmod +x "$CLEANUP_SCRIPT"

# 定时任务标识符
CRON_COMMENT="# Shell-API Log Cleanup Task"
CRON_JOB="$CRON_TIME LOG_DIR=\"$LOG_DIR\" KEEP_DAYS=\"$KEEP_DAYS\" \"$CLEANUP_SCRIPT\" >> \"$LOG_DIR/cleanup.log\" 2>&1 $CRON_COMMENT"

# 安装定时任务
install_cron() {
    log_info "安装日志清理定时任务..."
    log_info "执行时间: $CRON_TIME"
    log_info "日志目录: $LOG_DIR"
    log_info "保留天数: $KEEP_DAYS"
    log_info "清理脚本: $CLEANUP_SCRIPT"
    
    # 确保日志目录存在
    mkdir -p "$LOG_DIR"
    
    # 检查是否已存在相同的定时任务
    if crontab -l 2>/dev/null | grep -q "$CRON_COMMENT"; then
        log_warn "检测到已存在的定时任务，将先删除旧任务"
        uninstall_cron
    fi
    
    # 添加新的定时任务
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    
    if [[ $? -eq 0 ]]; then
        log_success "定时任务安装成功"
        log_info "任务将在以下时间执行: $CRON_TIME"
        log_info "日志输出到: $LOG_DIR/cleanup.log"
    else
        log_error "定时任务安装失败"
        exit 1
    fi
}

# 卸载定时任务
uninstall_cron() {
    log_info "卸载日志清理定时任务..."
    
    # 删除包含标识符的定时任务
    crontab -l 2>/dev/null | grep -v "$CRON_COMMENT" | crontab -
    
    if [[ $? -eq 0 ]]; then
        log_success "定时任务卸载成功"
    else
        log_error "定时任务卸载失败"
        exit 1
    fi
}

# 查看定时任务状态
status_cron() {
    log_info "查看定时任务状态..."
    
    if crontab -l 2>/dev/null | grep -q "$CRON_COMMENT"; then
        log_success "定时任务已安装"
        echo "当前定时任务:"
        crontab -l 2>/dev/null | grep "$CRON_COMMENT" -B1 -A1
    else
        log_warn "定时任务未安装"
    fi
    
    echo ""
    log_info "所有定时任务:"
    crontab -l 2>/dev/null || log_warn "没有找到任何定时任务"
}

# 测试清理脚本
test_cleanup() {
    log_info "测试日志清理脚本..."
    log_info "执行试运行模式..."
    
    LOG_DIR="$LOG_DIR" KEEP_DAYS="$KEEP_DAYS" DRY_RUN="true" "$CLEANUP_SCRIPT"
    
    if [[ $? -eq 0 ]]; then
        log_success "清理脚本测试成功"
    else
        log_error "清理脚本测试失败"
        exit 1
    fi
}

# 执行相应操作
case $ACTION in
    install)
        install_cron
        ;;
    uninstall)
        uninstall_cron
        ;;
    status)
        status_cron
        ;;
    test)
        test_cleanup
        ;;
    *)
        log_error "未知操作: $ACTION"
        show_help
        exit 1
        ;;
esac

exit 0
