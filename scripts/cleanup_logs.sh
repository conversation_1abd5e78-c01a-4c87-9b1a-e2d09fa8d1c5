#!/bin/bash

# 日志清理脚本
# 用于清理超过指定天数的日志文件

# 配置参数
LOG_DIR="${LOG_DIR:-./logs}"           # 日志目录，默认为 ./logs
KEEP_DAYS="${KEEP_DAYS:-7}"            # 保留天数，默认7天
DRY_RUN="${DRY_RUN:-false}"            # 是否为试运行模式

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
日志清理脚本

用法: $0 [选项]

选项:
    -d, --dir DIR       指定日志目录 (默认: ./logs)
    -k, --keep DAYS     指定保留天数 (默认: 7)
    -n, --dry-run       试运行模式，只显示将要删除的文件，不实际删除
    -h, --help          显示此帮助信息

环境变量:
    LOG_DIR             日志目录
    KEEP_DAYS           保留天数
    DRY_RUN             是否为试运行模式 (true/false)

示例:
    $0                                  # 使用默认设置
    $0 -d /var/log/myapp -k 3          # 清理 /var/log/myapp 目录中3天前的日志
    $0 --dry-run                       # 试运行模式
    LOG_DIR=/app/logs KEEP_DAYS=14 $0   # 使用环境变量

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--dir)
            LOG_DIR="$2"
            shift 2
            ;;
        -k|--keep)
            KEEP_DAYS="$2"
            shift 2
            ;;
        -n|--dry-run)
            DRY_RUN="true"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证参数
if [[ ! -d "$LOG_DIR" ]]; then
    log_error "日志目录不存在: $LOG_DIR"
    exit 1
fi

if ! [[ "$KEEP_DAYS" =~ ^[0-9]+$ ]] || [[ "$KEEP_DAYS" -lt 1 ]]; then
    log_error "保留天数必须是正整数: $KEEP_DAYS"
    exit 1
fi

# 显示配置信息
log_info "开始日志清理任务"
log_info "日志目录: $LOG_DIR"
log_info "保留天数: $KEEP_DAYS"
log_info "试运行模式: $DRY_RUN"

# 统计信息
total_files=0
deleted_files=0
total_size=0
deleted_size=0

# 查找并处理过期日志文件
log_info "查找超过 $KEEP_DAYS 天的日志文件..."

# 使用 find 命令查找过期文件
# -name "*.log*" 匹配所有日志文件
# -type f 只匹配文件
# -mtime +$KEEP_DAYS 匹配修改时间超过指定天数的文件
while IFS= read -r -d '' file; do
    if [[ -f "$file" ]]; then
        total_files=$((total_files + 1))
        file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
        total_size=$((total_size + file_size))
        
        # 获取文件信息
        file_date=$(stat -f%Sm -t%Y-%m-%d "$file" 2>/dev/null || stat -c%y "$file" 2>/dev/null | cut -d' ' -f1)
        file_size_mb=$(echo "scale=2; $file_size / 1024 / 1024" | bc -l 2>/dev/null || echo "0")
        
        if [[ "$DRY_RUN" == "true" ]]; then
            log_warn "[试运行] 将删除: $file (${file_date}, ${file_size_mb}MB)"
        else
            log_info "删除文件: $file (${file_date}, ${file_size_mb}MB)"
            if rm "$file"; then
                deleted_files=$((deleted_files + 1))
                deleted_size=$((deleted_size + file_size))
                log_success "已删除: $(basename "$file")"
            else
                log_error "删除失败: $file"
            fi
        fi
    fi
done < <(find "$LOG_DIR" -name "*.log*" -type f -mtime +$KEEP_DAYS -print0)

# 计算大小（MB）
total_size_mb=$(echo "scale=2; $total_size / 1024 / 1024" | bc -l 2>/dev/null || echo "0")
deleted_size_mb=$(echo "scale=2; $deleted_size / 1024 / 1024" | bc -l 2>/dev/null || echo "0")

# 显示统计信息
log_info "清理任务完成"
log_info "找到过期文件: $total_files 个 (${total_size_mb}MB)"

if [[ "$DRY_RUN" == "true" ]]; then
    log_warn "[试运行] 将删除文件: $total_files 个 (${total_size_mb}MB)"
else
    log_success "已删除文件: $deleted_files 个 (${deleted_size_mb}MB)"
    if [[ $deleted_files -lt $total_files ]]; then
        failed_files=$((total_files - deleted_files))
        log_warn "删除失败: $failed_files 个文件"
    fi
fi

# 显示当前日志目录状态
log_info "当前日志目录状态:"
if command -v ls >/dev/null 2>&1; then
    ls -lah "$LOG_DIR"/*.log* 2>/dev/null | head -10 || log_info "没有找到日志文件"
fi

exit 0
