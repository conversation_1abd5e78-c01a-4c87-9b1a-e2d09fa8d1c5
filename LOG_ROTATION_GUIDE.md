# 日志轮转问题解决方案

## 问题分析

你遇到的日志轮转和自动删除问题的根本原因是：

1. **`file-rotatelogs` 库已被归档**：该库在2021年7月19日被作者归档，不再维护，存在已知的bug
2. **MaxAge 功能不可靠**：特别是当同时使用 `WithRotationSize` 时，自动删除功能经常失效
3. **文件命名模式问题**：导致清理逻辑无法正确识别过期文件

## 解决方案

我为你提供了两个解决方案：

### 方案一：替换为 lumberjack（推荐）

已经将你的日志系统从 `file-rotatelogs` 替换为更可靠的 `lumberjack` 库。

#### 优势
- ✅ **活跃维护**：lumberjack 是一个活跃维护的项目
- ✅ **可靠的自动删除**：MaxAge 功能工作正常
- ✅ **简单配置**：配置更加直观
- ✅ **压缩支持**：支持自动压缩旧日志文件
- ✅ **更好的性能**：更高效的文件操作

#### 配置说明
```go
logger := &lumberjack.Logger{
    Filename:   filepath.Join(LogDir, "shell-api.log"), // 日志文件路径
    MaxSize:    int(config.LogsRotationSize),           // 单个文件最大大小（MB）
    MaxBackups: 10,                                     // 保留的旧文件数量
    MaxAge:     int(config.LogsMaxAge / 24),            // 保留天数（将小时转换为天）
    Compress:   true,                                   // 是否压缩旧文件
    LocalTime:  true,                                   // 使用本地时间
}
```

#### 环境变量配置
你的现有环境变量仍然有效：
- `LOGS_ROTATION_SIZE=10` # 单个文件最大10MB
- `LOGS_MAX_AGE=168` # 保留168小时（7天）

### 方案二：定时任务清理（备选）

如果你想保留现有的 `file-rotatelogs` 库，我也提供了定时任务脚本来手动清理过期日志。

#### 脚本功能
1. **`scripts/cleanup_logs.sh`** - 日志清理脚本
2. **`scripts/setup_log_cleanup_cron.sh`** - 定时任务管理脚本

#### 使用方法

##### 1. 测试清理脚本
```bash
# 试运行模式，查看将要删除的文件
./scripts/cleanup_logs.sh --dry-run

# 实际执行清理
./scripts/cleanup_logs.sh

# 自定义参数
./scripts/cleanup_logs.sh -d /path/to/logs -k 14  # 清理14天前的日志
```

##### 2. 设置定时任务
```bash
# 安装定时任务（每天凌晨2点执行）
./scripts/setup_log_cleanup_cron.sh install

# 自定义时间和参数
./scripts/setup_log_cleanup_cron.sh install -t "0 3 * * *" -k 14

# 查看定时任务状态
./scripts/setup_log_cleanup_cron.sh status

# 卸载定时任务
./scripts/setup_log_cleanup_cron.sh uninstall

# 测试清理脚本
./scripts/setup_log_cleanup_cron.sh test
```

##### 3. 定时任务时间格式
- `"0 2 * * *"` - 每天凌晨2点执行
- `"0 */6 * * *"` - 每6小时执行一次
- `"0 2 * * 0"` - 每周日凌晨2点执行

## 测试结果

### lumberjack 测试
- ✅ 日志轮转正常工作
- ✅ 文件大小达到限制时自动轮转
- ✅ 自动删除超过保留期的文件
- ✅ 文件命名规范：`shell-api-2025-07-27T16-34-09.028.log`

### 清理脚本测试
- ✅ 成功识别29个过期日志文件（8.88MB）
- ✅ 正确删除所有过期文件
- ✅ 保留最近7天的日志文件
- ✅ 试运行模式工作正常

## 推荐配置

### 生产环境推荐使用方案一（lumberjack）
```yaml
# docker-compose.yml
environment:
  - ROTATE_LOGS_ENABLED=true
  - LOGS_ROTATION_SIZE=50    # 50MB per file
  - LOGS_MAX_AGE=336         # 14天 (14 * 24 = 336小时)
```

### 如果使用方案二（定时任务）
```bash
# 每天凌晨3点清理14天前的日志
./scripts/setup_log_cleanup_cron.sh install -t "0 3 * * *" -k 14
```

## 监控和维护

### 检查日志状态
```bash
# 查看当前日志文件
ls -lah logs/

# 查看日志目录大小
du -sh logs/

# 查看定时任务状态
./scripts/setup_log_cleanup_cron.sh status

# 查看清理日志
tail -f logs/cleanup.log
```

### 故障排除
1. **如果lumberjack不工作**：检查文件权限和磁盘空间
2. **如果定时任务不执行**：检查cron服务状态和脚本权限
3. **如果清理脚本报错**：查看 `logs/cleanup.log` 文件

## 总结

**推荐使用方案一（lumberjack）**，因为：
1. 更可靠的自动轮转和删除
2. 更好的性能和维护性
3. 不需要额外的定时任务
4. 代码更简洁

如果你有任何问题或需要调整配置，请告诉我！
