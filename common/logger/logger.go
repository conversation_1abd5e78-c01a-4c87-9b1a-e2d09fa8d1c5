package logger

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	requestHelper "github.com/songquanpeng/one-api/common/helper/request"
	"gopkg.in/natefinch/lumberjack.v2"

	"github.com/gin-gonic/gin"

	"github.com/songquanpeng/one-api/common/config"
)

type loggerLevel string

const (
	loggerDEBUG loggerLevel = "DEBUG"
	loggerINFO  loggerLevel = "INFO"
	loggerWarn  loggerLevel = "WARN"
	loggerError loggerLevel = "ERROR"
	loggerFatal loggerLevel = "FATAL"
)

var (
	setupLogOnce sync.Once
	logsWriter   io.Writer
)

func getCallerInfo(skip int) string {
	_, file, line, ok := runtime.Caller(skip)
	if !ok {
		return ""
	}
	parts := strings.Split(file, "/")
	if len(parts) > 2 {
		file = strings.Join(parts[len(parts)-2:], "/")
	}
	return fmt.Sprintf("%s:%d", file, line)
}

// SetupLogger 初始化日志系统
func SetupLogger() error {
	var err error
	setupLogOnce.Do(func() {
		if LogDir != "" && !config.LogFileDisabled {
			if config.RotateLogsEnabled {
				logsWriter, err = setupRotateLogs()
			} else {
				logsWriter, err = setupSimpleLogs()
			}
			if err != nil {
				log.Printf("Failed to setup logger: %v", err)
				return
			}

			// 根据 LogDisabled 配置决定是否输出到控制台
			if config.LogDisabled {
				gin.DefaultWriter = logsWriter
				gin.DefaultErrorWriter = logsWriter
			} else {
				gin.DefaultWriter = io.MultiWriter(os.Stdout, logsWriter)
				gin.DefaultErrorWriter = io.MultiWriter(os.Stderr, logsWriter)
			}
		}
	})
	return err
}

// setupRotateLogs 设置轮转日志
func setupRotateLogs() (io.Writer, error) {
	// 确保日志目录存在
	if err := os.MkdirAll(LogDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %v", err)
	}

	// 主日志文件路径
	mainLogFile := filepath.Join(LogDir, "shell-api.log")

	// 使用 lumberjack 创建轮转日志写入器
	logger := &lumberjack.Logger{
		Filename:   mainLogFile,                  // 主日志文件
		MaxSize:    int(config.LogsRotationSize), // 单个文件最大大小（MB）
		MaxBackups: 50,                           // 保留足够多的备份文件，主要由MaxAge控制删除
		MaxAge:     int(config.LogsMaxAge / 24),  // 保留天数（将小时转换为天）
		Compress:   true,                         // 压缩旧文件节省空间
		LocalTime:  true,                         // 使用本地时间
	}

	// 创建或更新 current.log 软链接
	currentLinkPath := filepath.Join(LogDir, "current.log")
	if err := updateCurrentLogLink(currentLinkPath, mainLogFile); err != nil {
		// 软链接创建失败不应该阻止日志系统工作，只记录警告
		fmt.Printf("Warning: failed to create current.log symlink: %v\n", err)
	}

	// 验证写入器
	_, err := logger.Write([]byte("Logger initialized\n"))
	if err != nil {
		return nil, fmt.Errorf("failed to write to log file: %v", err)
	}

	return logger, nil
}

// updateCurrentLogLink 更新 current.log 软链接
func updateCurrentLogLink(linkPath, targetFile string) error {
	// 获取目标文件的相对路径（相对于链接文件的目录）
	targetFileName := filepath.Base(targetFile)

	// 如果软链接已存在，先删除
	if _, err := os.Lstat(linkPath); err == nil {
		if err := os.Remove(linkPath); err != nil {
			return fmt.Errorf("failed to remove existing symlink: %v", err)
		}
	}

	// 创建新的软链接
	if err := os.Symlink(targetFileName, linkPath); err != nil {
		return fmt.Errorf("failed to create symlink: %v", err)
	}

	return nil
}

// setupSimpleLogs 设置简单日志
func setupSimpleLogs() (io.Writer, error) {
	logPath := filepath.Join(LogDir, fmt.Sprintf("shellapi-%s.log", time.Now().Format("20060102")))
	fd, err := os.OpenFile(logPath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to open log file: %v", err)
	}
	return fd, nil
}

func SysLog(s string) {
	logHelper(nil, loggerINFO, s)
}

func SysLogf(format string, a ...any) {
	logHelper(nil, loggerINFO, fmt.Sprintf(format, a...))
}

func SysWarn(s string) {
	logHelper(nil, loggerWarn, s)
}

func SysWarnf(format string, a ...any) {
	logHelper(nil, loggerWarn, fmt.Sprintf(format, a...))
}

func SysError(s string) {
	logHelper(nil, loggerError, s)
}

func SysErrorf(format string, a ...any) {
	logHelper(nil, loggerError, fmt.Sprintf(format, a...))
}

func SysDebug(s string) {
	if config.DebugEnabled {
		t := time.Now()
		caller := getCallerInfo(2)
		_, _ = fmt.Fprintf(gin.DefaultWriter, "[DEBUG] %v | %s | %s\n",
			t.Format("2006/01/02 - 15:04:05"),
			caller,
			s)
	}
}

func Debug(ctx context.Context, msg string) {
	if !config.DebugEnabled {
		return
	}
	logHelper(ctx, loggerDEBUG, msg)
}

func Info(ctx context.Context, msg string) {
	logHelper(ctx, loggerINFO, msg)
}

func Warn(ctx context.Context, msg string) {
	logHelper(ctx, loggerWarn, msg)
}

func Error(ctx context.Context, msg string) {
	logHelper(ctx, loggerError, msg)
}

func Debugf(ctx context.Context, format string, a ...any) {
	if !config.DebugEnabled {
		return
	}
	logHelper(ctx, loggerDEBUG, fmt.Sprintf(format, a...))
}

func Infof(ctx context.Context, format string, a ...any) {
	logHelper(ctx, loggerINFO, fmt.Sprintf(format, a...))
}

func Warnf(ctx context.Context, format string, a ...any) {
	logHelper(ctx, loggerWarn, fmt.Sprintf(format, a...))
}

func Errorf(ctx context.Context, format string, a ...any) {
	logHelper(ctx, loggerError, fmt.Sprintf(format, a...))
}

func FatalLog(s string) {
	logHelper(nil, loggerFatal, s)
}

func FatalLogf(format string, a ...any) {
	logHelper(nil, loggerFatal, fmt.Sprintf(format, a...))
}

func logHelper(ctx context.Context, level loggerLevel, msg string) {
	writer := gin.DefaultErrorWriter
	if level == loggerINFO {
		writer = gin.DefaultWriter
	}
	var requestId string
	if ctx != nil {
		rawRequestId := requestHelper.GetRequestID(ctx)
		if rawRequestId != "" {
			requestId = fmt.Sprintf(" | %s", rawRequestId)
		}
	}
	lineInfo, funcName := getLineInfo()
	now := time.Now()
	_, _ = fmt.Fprintf(writer, "[%s] %v%s%s %s%s \n", level, now.Format("2006/01/02 - 15:04:05"), requestId, lineInfo, funcName, msg)
	SetupLogger()
	if level == loggerFatal {
		os.Exit(1)
	}
}

func getLineInfo() (string, string) {
	funcName := "[unknown] "
	pc, file, line, ok := runtime.Caller(3)
	if ok {
		if fn := runtime.FuncForPC(pc); fn != nil {
			parts := strings.Split(fn.Name(), ".")
			funcName = "[" + parts[len(parts)-1] + "] "
		}
	} else {
		file = "unknown"
		line = 0
	}
	parts := strings.Split(file, "one-api/")
	if len(parts) > 1 {
		file = parts[1]
	}
	return fmt.Sprintf(" | %s:%d", file, line), funcName
}
